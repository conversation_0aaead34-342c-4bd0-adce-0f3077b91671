version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: text-classification-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  api:
    build: .
    container_name: text-classification-api
    ports:
      - "8000:8000"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - DATABASE_URL=sqlite:///./data/text_classification.db
    volumes:
      - ./data:/app/data
      - ./.env:/app/.env
    depends_on:
      - redis
    restart: unless-stopped
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  worker:
    build: .
    container_name: text-classification-worker
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - DATABASE_URL=sqlite:///./data/text_classification.db
    volumes:
      - ./data:/app/data
      - ./.env:/app/.env
    depends_on:
      - redis
    restart: unless-stopped
    command: python start_worker.py

  flower:
    build: .
    container_name: text-classification-flower
    ports:
      - "5555:5555"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - redis
    restart: unless-stopped
    command: python start_flower.py

volumes:
  redis_data:
