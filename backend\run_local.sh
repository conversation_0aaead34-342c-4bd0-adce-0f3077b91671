#!/bin/bash

echo "Starting Text Classification System with Redis and Celery..."

# Check if Redis is running
echo "Checking Redis connection..."
if ! redis-cli ping > /dev/null 2>&1; then
    echo "Redis is not running. Please start Redis first."
    echo "You can install Redis with:"
    echo "  Ubuntu/Debian: sudo apt-get install redis-server"
    echo "  macOS: brew install redis"
    echo "  Or use Docker: docker run -d -p 6379:6379 redis:alpine"
    exit 1
fi

echo "Redis is running!"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

echo "Activating virtual environment..."
source venv/bin/activate

echo "Installing dependencies..."
pip install -r requirements.txt

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "Creating .env file..."
    cp .env.example .env
fi

echo "Starting services..."

# Start Celery worker in background
echo "Starting Celery worker..."
python start_worker.py &
WORKER_PID=$!

# Wait a bit for worker to start
sleep 3

# Start Flower monitoring in background
echo "Starting Flower monitoring dashboard..."
python start_flower.py &
FLOWER_PID=$!

# Wait a bit for Flower to start
sleep 3

# Function to cleanup background processes
cleanup() {
    echo "Stopping services..."
    kill $WORKER_PID 2>/dev/null
    kill $FLOWER_PID 2>/dev/null
    echo "Services stopped."
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Start FastAPI server
echo "Starting FastAPI server..."
echo ""
echo "Services will be available at:"
echo "- API: http://localhost:8000"
echo "- Flower Dashboard: http://localhost:5555 (admin/admin123)"
echo ""
echo "Press Ctrl+C to stop all services"
echo ""

python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload

# Cleanup when FastAPI server stops
cleanup
