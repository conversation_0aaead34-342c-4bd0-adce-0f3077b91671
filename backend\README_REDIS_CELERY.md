# Text Classification System với Redis và Celery

Hệ thống đã được tích hợp Redis và Celery để xử lý message queue, cho phép xử lý nhiều query đồng thời một cách hiệu quả.

## Tính năng mới

### 1. Message Queue với Redis và Celery
- **Redis**: Message broker và result backend
- **Celery**: Distributed task queue
- **Flower**: Web-based monitoring tool

### 2. Async API Endpoints
- `/async/classify` - Submit text classification task
- `/async/classify/batch` - Submit batch classification task
- `/async/tasks/{task_id}` - Get task status
- `/async/tasks/{task_id}` (DELETE) - Cancel task

### 3. Improved CSV Processing
- CSV processing now uses Celery tasks
- Better progress tracking
- Non-blocking uploads

## Cài đặt và Chạy

### Phương pháp 1: Chạy Local (Khuyến nghị)

#### Yêu cầu
- Python 3.8+
- Redis Server

#### Cài đặt Redis

**Windows:**
1. Download Redis từ: https://github.com/microsoftarchive/redis/releases
2. Hoặc sử dụng Docker: `docker run -d -p 6379:6379 redis:alpine`

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install redis-server
sudo systemctl start redis-server
```

**macOS:**
```bash
brew install redis
brew services start redis
```

#### Chạy hệ thống

**Windows:**
```cmd
cd backend
run_local.bat
```

**Linux/macOS:**
```bash
cd backend
chmod +x run_local.sh
./run_local.sh
```

### Phương pháp 2: Sử dụng Docker Compose

```bash
cd backend
docker-compose up -d
```

## Cấu hình

### Environment Variables (.env)
```env
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Database Configuration
DATABASE_URL=sqlite:///./data/text_classification.db

# JWT Configuration
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

## Kiến trúc hệ thống

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI App   │    │   Redis Broker  │    │  Celery Worker  │
│                 │    │                 │    │                 │
│ - REST API      │◄──►│ - Message Queue │◄──►│ - Task Executor │
│ - Task Submit   │    │ - Result Store  │    │ - ML Processing │
│ - Status Check  │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   SQLite DB     │    │  Flower Monitor │    │   ML Models     │
│                 │    │                 │    │                 │
│ - Task Status   │    │ - Web Dashboard │    │ - HuggingFace   │
│ - Results       │    │ - Task Monitor  │    │ - Transformers  │
│ - User Data     │    │ - Worker Stats  │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Sử dụng API

### 1. Sync Classification (Cũ)
```bash
curl -X POST "http://localhost:8000/classify" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "I love this product!",
    "model_type": "sentiment"
  }'
```

### 2. Async Classification (Mới)
```bash
# Submit task
curl -X POST "http://localhost:8000/async/classify" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "I love this product!",
    "model_type": "sentiment",
    "priority": 5
  }'

# Response: {"task_id": "abc-123", "status": "PENDING"}

# Check status
curl "http://localhost:8000/async/tasks/abc-123"

# Response: 
# {
#   "task_id": "abc-123",
#   "status": "SUCCESS",
#   "progress": 100,
#   "result": {...}
# }
```

### 3. Batch Processing
```bash
curl -X POST "http://localhost:8000/async/classify/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "texts": ["Text 1", "Text 2", "Text 3"],
    "model_type": "sentiment",
    "batch_size": 16
  }'
```

## Monitoring

### Flower Dashboard
- URL: http://localhost:5555
- Username: admin
- Password: admin123

### Features:
- Real-time task monitoring
- Worker status
- Task history
- Performance metrics

## Task Queues

### Queue Types:
1. **classification** - Single text classification
2. **batch_processing** - Batch text classification  
3. **csv_processing** - CSV file processing

### Priority Levels:
- 1-3: High priority (CSV processing)
- 4-6: Normal priority (regular tasks)
- 7-10: Low priority (background tasks)

## Performance

### Concurrent Processing:
- **Worker processes**: 4 (configurable)
- **Queue capacity**: Unlimited (Redis)
- **Task timeout**: 10 minutes
- **Retry attempts**: 3

### Scalability:
- Horizontal scaling: Add more workers
- Vertical scaling: Increase worker concurrency
- Load balancing: Multiple API instances

## Troubleshooting

### Common Issues:

1. **Redis connection failed**
   ```bash
   # Check Redis status
   redis-cli ping
   
   # Start Redis
   redis-server
   ```

2. **Worker not processing tasks**
   ```bash
   # Check worker logs
   celery -A celery_app worker --loglevel=info
   
   # Check Flower dashboard
   # http://localhost:5555
   ```

3. **Task stuck in PENDING**
   - Check worker is running
   - Check Redis connection
   - Check task queue name

4. **Memory issues**
   - Reduce batch size
   - Increase worker memory limit
   - Add more workers

### Logs:
- API logs: Console output
- Worker logs: Console output
- Redis logs: Redis server logs
- Flower logs: Flower dashboard

## Development

### Adding New Task Types:
1. Create task in `tasks/` directory
2. Register in `celery_app.py`
3. Add API endpoint in `main.py`
4. Update task service

### Testing:
```bash
# Test Redis connection
redis-cli ping

# Test Celery worker
celery -A celery_app inspect active

# Test API endpoints
curl http://localhost:8000/health
```

## Production Deployment

### Recommendations:
1. Use Redis Cluster for high availability
2. Use multiple worker nodes
3. Set up proper monitoring (Prometheus + Grafana)
4. Use reverse proxy (Nginx)
5. Set up SSL/TLS
6. Configure proper logging
7. Set up backup for Redis data

### Security:
- Change default Flower credentials
- Use Redis AUTH
- Set up firewall rules
- Use environment variables for secrets
