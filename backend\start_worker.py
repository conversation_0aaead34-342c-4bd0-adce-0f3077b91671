#!/usr/bin/env python3
"""
Celery Worker Startup Script
"""
import os
import sys
from celery_app import celery_app

if __name__ == "__main__":
    # Set up logging
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # Start the worker
    celery_app.worker_main([
        'worker',
        '--loglevel=info',
        '--concurrency=4',  # Number of worker processes
        '--queues=classification,batch_processing,csv_processing',
        '--hostname=worker@%h'
    ])
