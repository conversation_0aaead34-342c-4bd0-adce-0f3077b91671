#!/usr/bin/env python3
"""
Test script for async API endpoints
"""
import requests
import time
import json

BASE_URL = "http://localhost:8000"

def test_async_classification():
    """Test async text classification"""
    print("Testing async text classification...")
    
    # Submit task
    response = requests.post(f"{BASE_URL}/async/classify", json={
        "text": "I love this product! It's amazing!",
        "model_type": "sentiment",
        "priority": 5
    })
    
    if response.status_code == 200:
        result = response.json()
        task_id = result["task_id"]
        print(f"Task submitted: {task_id}")
        
        # Check status
        while True:
            status_response = requests.get(f"{BASE_URL}/async/tasks/{task_id}")
            if status_response.status_code == 200:
                status = status_response.json()
                print(f"Status: {status['status']}, Progress: {status['progress']}%")
                
                if status["status"] in ["SUCCESS", "FAILURE"]:
                    if status["status"] == "SUCCESS":
                        print("Result:", json.dumps(status["result"], indent=2))
                    else:
                        print("Error:", status["error_message"])
                    break
                    
                time.sleep(2)
            else:
                print(f"Error checking status: {status_response.status_code}")
                break
    else:
        print(f"Error submitting task: {response.status_code}")
        print(response.text)

def test_async_batch():
    """Test async batch classification"""
    print("\nTesting async batch classification...")
    
    # Submit batch task
    response = requests.post(f"{BASE_URL}/async/classify/batch", json={
        "texts": [
            "I love this product!",
            "This is terrible!",
            "It's okay, nothing special",
            "Amazing quality!",
            "Worst purchase ever"
        ],
        "model_type": "sentiment",
        "batch_size": 2,
        "priority": 3
    })
    
    if response.status_code == 200:
        result = response.json()
        task_id = result["task_id"]
        print(f"Batch task submitted: {task_id}")
        
        # Check status
        while True:
            status_response = requests.get(f"{BASE_URL}/async/tasks/{task_id}")
            if status_response.status_code == 200:
                status = status_response.json()
                print(f"Status: {status['status']}, Progress: {status['progress']}%")
                
                if status["status"] in ["SUCCESS", "FAILURE"]:
                    if status["status"] == "SUCCESS":
                        print("Batch results:")
                        for i, result in enumerate(status["result"]["results"]):
                            print(f"  {i+1}. {result['text'][:30]}... -> {result['prediction']} ({result['confidence']:.2f})")
                    else:
                        print("Error:", status["error_message"])
                    break
                    
                time.sleep(2)
            else:
                print(f"Error checking status: {status_response.status_code}")
                break
    else:
        print(f"Error submitting batch task: {response.status_code}")
        print(response.text)

def test_health():
    """Test health endpoint"""
    print("\nTesting health endpoint...")
    
    response = requests.get(f"{BASE_URL}/health")
    if response.status_code == 200:
        health = response.json()
        print("Health status:", json.dumps(health, indent=2))
    else:
        print(f"Error checking health: {response.status_code}")

def test_models():
    """Test models endpoint"""
    print("\nTesting models endpoint...")
    
    response = requests.get(f"{BASE_URL}/models")
    if response.status_code == 200:
        models = response.json()
        print("Available models:")
        for model in models["available_models"]:
            print(f"  - {model['name']}: {model['description']}")
    else:
        print(f"Error getting models: {response.status_code}")

if __name__ == "__main__":
    print("Testing Text Classification API with Redis/Celery")
    print("=" * 50)
    
    try:
        # Test basic endpoints first
        test_health()
        test_models()
        
        # Test async endpoints
        test_async_classification()
        test_async_batch()
        
        print("\n" + "=" * 50)
        print("All tests completed!")
        print("Check Flower dashboard at: http://localhost:5555")
        
    except requests.exceptions.ConnectionError:
        print("Error: Cannot connect to API server.")
        print("Make sure the server is running at http://localhost:8000")
    except KeyboardInterrupt:
        print("\nTests interrupted by user.")
    except Exception as e:
        print(f"Unexpected error: {e}")
