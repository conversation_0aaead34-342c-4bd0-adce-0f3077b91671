#!/usr/bin/env python3
"""
Flower Monitoring Dashboard Startup Script
"""
import os
import sys
import subprocess

if __name__ == "__main__":
    print("Starting Flower monitoring dashboard...")

    # Set environment variables for Flower
    os.environ.setdefault('FLOWER_BROKER', 'redis://localhost:6379/0')
    os.environ.setdefault('FLOWER_PORT', '5555')

    try:
        # Use subprocess to run celery flower command
        cmd = [
            sys.executable, '-m', 'celery',
            '-A', 'celery_app',
            'flower',
            '--broker=redis://localhost:6379/0',
            '--port=5555',
            '--basic_auth=admin:admin123'
        ]

        print(f"Running command: {' '.join(cmd)}")
        print("Flower will be available at: http://localhost:5555")
        print("Username: admin, Password: admin123")
        print("Press Ctrl+C to stop...")

        # Run the command
        subprocess.run(cmd, check=True)

    except subprocess.CalledProcessError as e:
        print(f"Error starting Flower: {e}")
        print("\nAlternative ways to start Flower:")
        print("1. celery -A celery_app flower --port=5555 --basic_auth=admin:admin123")
        print("2. python -m flower --broker=redis://localhost:6379/0 --port=5555")

    except KeyboardInterrupt:
        print("\nFlower stopped by user.")

    except Exception as e:
        print(f"Unexpected error: {e}")
        print("\nPlease try running manually:")
        print("celery -A celery_app flower --port=5555 --basic_auth=admin:admin123")
