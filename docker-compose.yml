version: '3.8'

services:
  # Backend API
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: text-classification-backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./data/text_classification.db
      - PYTHONPATH=/app
    volumes:
      - ./backend:/app
      - backend_data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: text-classification-frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis (for future use with Celery)
  redis:
    image: redis:7-alpine
    container_name: text-classification-redis
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  backend_data:

networks:
  default:
    name: text-classification-network
