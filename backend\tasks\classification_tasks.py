import asyncio
import logging
import time
from typing import List, Dict, Any, Optional, Union
from datetime import datetime

from celery import current_task
from celery_app import celery_app
from services.text_classifier import TextClassifierService
from services.language_detector import LanguageDetectorService
from models.database import init_db, get_db, save_classification_result, User
from models.schemas import TextClassificationRequest
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

# Initialize services (these will be initialized in each worker)
text_classifier = None
language_detector = None

def get_services():
    """Initialize services if not already done"""
    global text_classifier, language_detector
    
    if text_classifier is None:
        text_classifier = TextClassifierService()
        # Run async initialization in sync context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(text_classifier.initialize())
        loop.close()
        
    if language_detector is None:
        language_detector = LanguageDetectorService()
    
    return text_classifier, language_detector

@celery_app.task(bind=True, name="tasks.classification_tasks.classify_text_task")
def classify_text_task(
    self,
    text: str,
    model_type: str,
    temperature: float = 1.0,
    model_selection: Union[str, List[str]] = "all",
    user_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    Celery task for single text classification
    
    Args:
        text: Input text to classify
        model_type: Type of classification (sentiment, spam, topic)
        temperature: Temperature for softmax scaling
        model_selection: Which models to use
        user_id: Optional user ID for saving results
        
    Returns:
        Classification result dictionary
    """
    try:
        # Update task state
        self.update_state(
            state="PROCESSING",
            meta={"message": "Initializing classification..."}
        )
        
        # Get services
        classifier, lang_detector = get_services()
        
        # Update task state
        self.update_state(
            state="PROCESSING",
            meta={"message": "Detecting language..."}
        )
        
        # Detect language
        detected_language = lang_detector.detect(text)
        
        # Update task state
        self.update_state(
            state="PROCESSING",
            meta={"message": f"Classifying text (language: {detected_language})..."}
        )
        
        # Classify text - run async function in sync context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            result = loop.run_until_complete(
                classifier.classify(
                    text=text,
                    model_type=model_type,
                    language=detected_language,
                    temperature=temperature,
                    model_selection=model_selection
                )
            )
        finally:
            loop.close()
        
        # Save result to database if user is authenticated
        if user_id:
            try:
                # Initialize database connection
                init_db()
                db = next(get_db())
                
                save_classification_result(
                    db=db,
                    text=text,
                    model_type=model_type,
                    prediction=result["prediction"],
                    confidence=result["confidence"],
                    language=detected_language,
                    processing_time=result.get("processing_time", 0),
                    user_id=user_id
                )
                db.close()
            except Exception as e:
                logger.error(f"Failed to save classification result: {e}")
        
        # Prepare final result
        final_result = {
            "text": text,
            "model_type": model_type,
            "prediction": result["prediction"],
            "confidence": result["confidence"],
            "all_scores": result["all_scores"],
            "temperature": result["temperature"],
            "language": detected_language,
            "processing_time": result.get("processing_time", 0),
            "timestamp": datetime.now().isoformat(),
            "is_ensemble": result.get("is_ensemble", False),
            "models_used": result.get("models_used", []),
            "individual_results": result.get("individual_results"),
            "task_id": self.request.id
        }
        
        return final_result
        
    except Exception as e:
        logger.error(f"Classification task error: {str(e)}")
        self.update_state(
            state="FAILURE",
            meta={"error": str(e), "message": "Classification failed"}
        )
        raise

@celery_app.task(bind=True, name="tasks.classification_tasks.classify_batch_task")
def classify_batch_task(
    self,
    texts: List[str],
    model_type: str,
    batch_size: int = 10,
    temperature: float = 1.0,
    model_selection: Union[str, List[str]] = "all"
) -> Dict[str, Any]:
    """
    Celery task for batch text classification
    
    Args:
        texts: List of input texts to classify
        model_type: Type of classification
        batch_size: Batch size for processing
        temperature: Temperature for softmax scaling
        model_selection: Which models to use
        
    Returns:
        Batch classification results
    """
    try:
        total_texts = len(texts)
        
        # Update task state
        self.update_state(
            state="PROCESSING",
            meta={
                "message": "Starting batch classification...",
                "total": total_texts,
                "processed": 0,
                "progress": 0
            }
        )
        
        # Get services
        classifier, lang_detector = get_services()
        
        results = []
        
        # Process texts in batches
        for i in range(0, total_texts, batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_start = i
            batch_end = min(i + batch_size, total_texts)
            
            # Update progress
            progress = int((i / total_texts) * 100)
            self.update_state(
                state="PROCESSING",
                meta={
                    "message": f"Processing batch {batch_start + 1}-{batch_end} of {total_texts}...",
                    "total": total_texts,
                    "processed": i,
                    "progress": progress
                }
            )
            
            # Process batch - run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                batch_results = loop.run_until_complete(
                    classifier.classify_batch(
                        texts=batch_texts,
                        model_type=model_type,
                        batch_size=len(batch_texts),
                        language=None,  # Auto-detect for each text
                        temperature=temperature,
                        model_selection=model_selection
                    )
                )
                
                # Add batch results to overall results
                for j, result in enumerate(batch_results):
                    results.append({
                        "index": batch_start + j,
                        "text": batch_texts[j],
                        "prediction": result["prediction"],
                        "confidence": result["confidence"],
                        "language": result["detected_language"],
                        "processing_time": result["processing_time"],
                        "is_ensemble": result.get("is_ensemble", False),
                        "models_used": result.get("models_used", [])
                    })
                    
            finally:
                loop.close()
        
        # Final result
        final_result = {
            "model_type": model_type,
            "total_processed": len(results),
            "results": results,
            "timestamp": datetime.now().isoformat(),
            "task_id": self.request.id,
            "batch_size": batch_size
        }
        
        return final_result
        
    except Exception as e:
        logger.error(f"Batch classification task error: {str(e)}")
        self.update_state(
            state="FAILURE",
            meta={"error": str(e), "message": "Batch classification failed"}
        )
        raise
