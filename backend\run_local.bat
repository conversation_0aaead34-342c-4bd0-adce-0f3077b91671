@echo off
echo Starting Text Classification System with Redis and Celery...

REM Check if Redis is running
echo Checking Redis connection...
redis-cli ping >nul 2>&1
if %errorlevel% neq 0 (
    echo Redis is not running. Please start Redis first.
    echo You can download Redis from: https://github.com/microsoftarchive/redis/releases
    echo Or use Docker: docker run -d -p 6379:6379 redis:alpine
    pause
    exit /b 1
)

echo Redis is running!

REM Install dependencies if needed
if not exist "venv\" (
    echo Creating virtual environment...
    python -m venv venv
)

echo Activating virtual environment...
call venv\Scripts\activate.bat

@REM echo Installing dependencies...
@REM pip install -r requirements.txt

REM Create .env file if it doesn't exist
if not exist ".env" (
    echo Creating .env file...
    copy .env.example .env
)

echo Starting services...

REM Start Celery worker in background
echo Starting Celery worker...
start "Celery Worker" cmd /k "venv\Scripts\activate.bat && python start_worker.py"

REM Wait a bit for worker to start
timeout /t 3 /nobreak >nul

REM Start Flower monitoring (optional)
echo Starting Flower monitoring dashboard...
start "Flower Dashboard" cmd /k "venv\Scripts\activate.bat && python start_flower.py"

REM Wait a bit for Flower to start
timeout /t 3 /nobreak >nul

REM Start FastAPI server
echo Starting FastAPI server...
echo.
echo Services will be available at:
echo - API: http://localhost:8000
echo - Flower Dashboard: http://localhost:5555 (admin/admin123)
echo.
echo Press Ctrl+C to stop the API server
echo Close other windows to stop worker and monitoring
echo.

python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload

echo API server stopped.
pause
