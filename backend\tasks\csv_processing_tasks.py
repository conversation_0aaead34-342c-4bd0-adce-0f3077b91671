import asyncio
import logging
import pandas as pd
import time
import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime
from io import String<PERSON>

from celery import current_task
from celery_app import celery_app
from services.text_classifier import TextClassifierService
from services.language_detector import LanguageDetectorService
from models.database import init_db, get_db, CSVProcessingJob, CSVResult
from models.schemas import CSVUploadRequest
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

# Initialize services (these will be initialized in each worker)
text_classifier = None
language_detector = None

def get_services():
    """Initialize services if not already done"""
    global text_classifier, language_detector
    
    if text_classifier is None:
        text_classifier = TextClassifierService()
        # Run async initialization in sync context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(text_classifier.initialize())
        loop.close()
        
    if language_detector is None:
        language_detector = LanguageDetectorService()
    
    return text_classifier, language_detector

@celery_app.task(bind=True, name="tasks.csv_processing_tasks.process_csv_task")
def process_csv_task(
    self,
    job_id: str,
    file_content: str,
    model_type: str,
    batch_size: int,
    text_column: str,
    model_selection: str = "all"
) -> Dict[str, Any]:
    """
    Celery task for processing CSV files with batch text classification
    
    Args:
        job_id: Unique job identifier
        file_content: CSV file content as string
        model_type: Type of classification
        batch_size: Batch size for processing
        text_column: Name of the text column in CSV
        model_selection: Which models to use
        
    Returns:
        Processing results
    """
    try:
        # Initialize database connection
        init_db()
        db = next(get_db())
        
        # Update task state
        self.update_state(
            state="PROCESSING",
            meta={
                "message": "Reading CSV file...",
                "job_id": job_id,
                "progress": 0
            }
        )
        
        # Read CSV data
        df = pd.read_csv(StringIO(file_content))
        total_rows = len(df)
        
        # Update job status in database
        job = db.query(CSVProcessingJob).filter(CSVProcessingJob.job_id == job_id).first()
        if job:
            job.status = "processing"
            job.total_rows = total_rows
            db.commit()
        
        # Get services
        classifier, lang_detector = get_services()
        
        # Update task state
        self.update_state(
            state="PROCESSING",
            meta={
                "message": "Starting text classification...",
                "job_id": job_id,
                "total_rows": total_rows,
                "processed_rows": 0,
                "progress": 0
            }
        )
        
        processed_rows = 0
        total_batches = (total_rows + batch_size - 1) // batch_size
        
        # Process data in batches
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, total_rows)
            
            batch_df = df.iloc[start_idx:end_idx]
            batch_texts = batch_df[text_column].tolist()
            batch_indices = batch_df.index.tolist()
            
            # Update progress
            progress = int((processed_rows / total_rows) * 100)
            self.update_state(
                state="PROCESSING",
                meta={
                    "message": f"Processing batch {batch_num + 1}/{total_batches}...",
                    "job_id": job_id,
                    "total_rows": total_rows,
                    "processed_rows": processed_rows,
                    "current_batch": batch_num + 1,
                    "total_batches": total_batches,
                    "progress": progress
                }
            )
            
            try:
                # Process batch using async function in sync context
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    batch_results = loop.run_until_complete(
                        classifier.classify_batch(
                            texts=batch_texts,
                            model_type=model_type,
                            batch_size=len(batch_texts),
                            language=None,  # Auto-detect for each text
                            model_selection=model_selection
                        )
                    )
                finally:
                    loop.close()
                
                # Save results to database
                for i, (idx, text, result) in enumerate(zip(batch_indices, batch_texts, batch_results)):
                    try:
                        csv_result = CSVResult(
                            job_id=job_id,
                            row_index=idx,
                            text=text,
                            prediction=result["prediction"],
                            confidence=result["confidence"],
                            language=result["detected_language"],
                            processing_time=result["processing_time"]
                        )
                        db.add(csv_result)
                        processed_rows += 1
                        
                    except Exception as e:
                        logger.error(f"Error saving result for row {idx}: {e}")
                        # Save error result
                        csv_result = CSVResult(
                            job_id=job_id,
                            row_index=idx,
                            text=text,
                            prediction="error",
                            confidence=0.0,
                            language="unknown",
                            processing_time=0.0,
                            error_message=str(e)
                        )
                        db.add(csv_result)
                        processed_rows += 1
                
                # Commit batch results
                db.commit()
                
                # Update job progress in database
                if job:
                    job.processed_rows = processed_rows
                    job.progress_percentage = (processed_rows / total_rows) * 100
                    db.commit()
                
            except Exception as e:
                logger.error(f"Error processing batch {batch_num}: {e}")
                # Save error results for the entire batch
                for idx, text in zip(batch_indices, batch_texts):
                    csv_result = CSVResult(
                        job_id=job_id,
                        row_index=idx,
                        text=text,
                        prediction="error",
                        confidence=0.0,
                        language="unknown",
                        processing_time=0.0,
                        error_message=str(e)
                    )
                    db.add(csv_result)
                    processed_rows += 1
                
                db.commit()
                
                # Update job progress
                if job:
                    job.processed_rows = processed_rows
                    job.progress_percentage = (processed_rows / total_rows) * 100
                    db.commit()
        
        # Mark job as completed
        if job:
            job.status = "completed"
            job.completed_at = datetime.utcnow()
            job.processing_time = (datetime.utcnow() - job.started_at).total_seconds()
            db.commit()
        
        # Final result
        final_result = {
            "job_id": job_id,
            "status": "completed",
            "total_rows": total_rows,
            "processed_rows": processed_rows,
            "model_type": model_type,
            "batch_size": batch_size,
            "task_id": self.request.id,
            "completed_at": datetime.now().isoformat()
        }
        
        db.close()
        return final_result
        
    except Exception as e:
        logger.error(f"CSV processing task error: {str(e)}")
        
        # Update job status to failed
        try:
            if 'db' in locals() and 'job' in locals() and job:
                job.status = "failed"
                job.completed_at = datetime.utcnow()
                job.error_message = str(e)
                db.commit()
            if 'db' in locals():
                db.close()
        except Exception as db_error:
            logger.error(f"Error updating job status: {db_error}")
        
        self.update_state(
            state="FAILURE",
            meta={
                "error": str(e),
                "message": "CSV processing failed",
                "job_id": job_id
            }
        )
        raise
