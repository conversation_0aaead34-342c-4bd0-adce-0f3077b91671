import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from celery_app import celery_app
from models.database import TaskStatus, get_db, init_db
from models.schemas import TaskStatusResponse, TaskSubmissionResponse

logger = logging.getLogger(__name__)

class TaskService:
    """Service for managing Celery tasks and their status"""
    
    def __init__(self):
        self.celery_app = celery_app
    
    def submit_text_classification_task(
        self,
        text: str,
        model_type: str,
        temperature: float = 1.0,
        model_selection: str = "all",
        user_id: Optional[int] = None,
        priority: int = 5
    ) -> TaskSubmissionResponse:
        """
        Submit a text classification task to Celery
        
        Args:
            text: Text to classify
            model_type: Type of classification
            temperature: Temperature for softmax scaling
            model_selection: Which models to use
            user_id: Optional user ID
            priority: Task priority (1=highest, 10=lowest)
            
        Returns:
            Task submission response
        """
        try:
            # Submit task to Celery
            task = celery_app.send_task(
                "tasks.classification_tasks.classify_text_task",
                args=[text, model_type, temperature, model_selection, user_id],
                priority=priority,
                countdown=0  # Execute immediately
            )
            
            # Save task status to database
            self._save_task_status(
                task_id=task.id,
                task_name="classify_text_task",
                status="PENDING",
                user_id=user_id
            )
            
            # Estimate completion time (rough estimate)
            estimated_completion = datetime.now() + timedelta(seconds=30)
            
            return TaskSubmissionResponse(
                task_id=task.id,
                message="Text classification task submitted successfully",
                status="PENDING",
                estimated_completion=estimated_completion
            )
            
        except Exception as e:
            logger.error(f"Error submitting text classification task: {e}")
            raise
    
    def submit_batch_classification_task(
        self,
        texts: List[str],
        model_type: str,
        batch_size: int = 16,
        temperature: float = 1.0,
        model_selection: str = "all",
        user_id: Optional[int] = None,
        priority: int = 5
    ) -> TaskSubmissionResponse:
        """
        Submit a batch classification task to Celery
        
        Args:
            texts: List of texts to classify
            model_type: Type of classification
            batch_size: Batch size for processing
            temperature: Temperature for softmax scaling
            model_selection: Which models to use
            user_id: Optional user ID
            priority: Task priority
            
        Returns:
            Task submission response
        """
        try:
            # Submit task to Celery
            task = celery_app.send_task(
                "tasks.classification_tasks.classify_batch_task",
                args=[texts, model_type, batch_size, temperature, model_selection],
                priority=priority,
                countdown=0
            )
            
            # Save task status to database
            self._save_task_status(
                task_id=task.id,
                task_name="classify_batch_task",
                status="PENDING",
                user_id=user_id
            )
            
            # Estimate completion time based on number of texts
            estimated_seconds = len(texts) * 2  # Rough estimate: 2 seconds per text
            estimated_completion = datetime.now() + timedelta(seconds=estimated_seconds)
            
            return TaskSubmissionResponse(
                task_id=task.id,
                message="Batch classification task submitted successfully",
                status="PENDING",
                estimated_completion=estimated_completion
            )
            
        except Exception as e:
            logger.error(f"Error submitting batch classification task: {e}")
            raise
    
    def submit_csv_processing_task(
        self,
        job_id: str,
        file_content: str,
        model_type: str,
        batch_size: int,
        text_column: str,
        model_selection: str = "all",
        user_id: Optional[int] = None,
        priority: int = 3  # Higher priority for CSV processing
    ) -> TaskSubmissionResponse:
        """
        Submit a CSV processing task to Celery
        
        Args:
            job_id: Unique job identifier
            file_content: CSV file content
            model_type: Type of classification
            batch_size: Batch size for processing
            text_column: Name of text column
            model_selection: Which models to use
            user_id: Optional user ID
            priority: Task priority
            
        Returns:
            Task submission response
        """
        try:
            # Submit task to Celery
            task = celery_app.send_task(
                "tasks.csv_processing_tasks.process_csv_task",
                args=[job_id, file_content, model_type, batch_size, text_column, model_selection],
                priority=priority,
                countdown=0
            )
            
            # Save task status to database
            self._save_task_status(
                task_id=task.id,
                task_name="process_csv_task",
                status="PENDING",
                user_id=user_id
            )
            
            # Estimate completion time based on file size
            estimated_seconds = len(file_content) // 1000  # Rough estimate
            estimated_completion = datetime.now() + timedelta(seconds=max(60, estimated_seconds))
            
            return TaskSubmissionResponse(
                task_id=task.id,
                message="CSV processing task submitted successfully",
                status="PENDING",
                estimated_completion=estimated_completion
            )
            
        except Exception as e:
            logger.error(f"Error submitting CSV processing task: {e}")
            raise
    
    def get_task_status(self, task_id: str) -> Optional[TaskStatusResponse]:
        """
        Get the current status of a task
        
        Args:
            task_id: Task identifier
            
        Returns:
            Task status response or None if not found
        """
        try:
            # Get task status from Celery
            task_result = celery_app.AsyncResult(task_id)
            
            # Get task status from database
            db = next(get_db())
            try:
                db_task = db.query(TaskStatus).filter(TaskStatus.task_id == task_id).first()
                
                if not db_task:
                    return None
                
                # Get additional info from Celery task result
                celery_status = task_result.status
                celery_info = task_result.info if task_result.info else {}
                
                # Determine progress
                progress = 0
                message = None
                result = None
                error_message = None
                
                if celery_status == "PENDING":
                    progress = 0
                    message = "Task is waiting to be processed"
                elif celery_status == "PROCESSING":
                    progress = celery_info.get("progress", 50)
                    message = celery_info.get("message", "Task is being processed")
                elif celery_status == "SUCCESS":
                    progress = 100
                    message = "Task completed successfully"
                    result = task_result.result
                elif celery_status == "FAILURE":
                    progress = 0
                    message = "Task failed"
                    error_message = str(celery_info.get("error", "Unknown error"))
                
                # Update database record
                db_task.status = celery_status
                db_task.progress = progress
                if result:
                    db_task.result = json.dumps(result)
                if error_message:
                    db_task.error_message = error_message
                db_task.updated_at = datetime.utcnow()
                db.commit()
                
                return TaskStatusResponse(
                    task_id=task_id,
                    task_name=db_task.task_name,
                    status=celery_status,
                    progress=progress,
                    message=message,
                    result=result,
                    error_message=error_message,
                    created_at=db_task.created_at,
                    updated_at=db_task.updated_at
                )
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error getting task status: {e}")
            return None
    
    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a running task
        
        Args:
            task_id: Task identifier
            
        Returns:
            True if task was cancelled, False otherwise
        """
        try:
            # Revoke task in Celery
            celery_app.control.revoke(task_id, terminate=True)
            
            # Update database status
            db = next(get_db())
            try:
                db_task = db.query(TaskStatus).filter(TaskStatus.task_id == task_id).first()
                if db_task:
                    db_task.status = "REVOKED"
                    db_task.error_message = "Task was cancelled by user"
                    db_task.updated_at = datetime.utcnow()
                    db.commit()
                
                return True
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error cancelling task: {e}")
            return False
    
    def _save_task_status(
        self,
        task_id: str,
        task_name: str,
        status: str,
        user_id: Optional[int] = None
    ):
        """Save task status to database"""
        try:
            init_db()
            db = next(get_db())
            try:
                task_status = TaskStatus(
                    task_id=task_id,
                    task_name=task_name,
                    status=status,
                    user_id=user_id
                )
                db.add(task_status)
                db.commit()
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error saving task status: {e}")

# Global task service instance
task_service = TaskService()
